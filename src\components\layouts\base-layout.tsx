import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { Outlet } from "react-router";
import Footer from "../footer";
import Navbar from "../navbar";

export default function BaseLayout() {
  return (
    <div className="gradient-bg min-h-screen">
      <SidebarProvider defaultOpen={false}>
        <AppSidebar />
        <SidebarInset className="bg-transparent">
          <Navbar />
          <main className="animate-fade-in flex-1 p-4 md:p-6 lg:p-8">
            <div>
              <Outlet />
            </div>
          </main>
          <Footer />
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}
