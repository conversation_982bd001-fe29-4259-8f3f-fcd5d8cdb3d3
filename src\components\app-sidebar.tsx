import {
  BarChart3,
  Car,
  Gauge,
  Home,
  MapPin,
  Route,
  Settings,
  Shield,
  Users,
  Zap,
} from "lucide-react";
import * as React from "react";

import { localeAtom } from "@/atoms/locale-atom";
import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenuButton,
  SidebarRail,
} from "@/components/ui/sidebar";
import { URLS } from "@/utils/urls";

// Modern GPS Tracking Navigation Data
const data = {
  navMain: [
    {
      title: "Dashboard",
      url: "/",
      icon: Home,
      isActive: true,
      badge: "Live",
      badgeVariant: "success" as const,
    },
    {
      title: "Fleet Units",
      url: URLS.units,
      icon: Car,
      isActive: true,
      badge: "15",
      badgeVariant: "primary" as const,
    },
    {
      title: "Live Tracking",
      url: "/tracking",
      icon: MapPin,
      isActive: true,
      badge: "12",
      badgeVariant: "success" as const,
    },
    {
      title: "Routes",
      url: "/routes",
      icon: Route,
      isActive: true,
    },
    {
      title: "Analytics",
      url: URLS.reports,
      icon: BarChart3,
      isActive: true,
      items: [
        {
          title: "Fleet Performance",
          url: URLS.reports,
          icon: Gauge,
        },
        {
          title: "Fuel Analysis",
          url: "/reports/fuel",
          icon: Zap,
        },
        {
          title: "Driver Reports",
          url: "/reports/drivers",
          icon: Users,
        },
      ],
    },
    {
      title: "Geofences",
      url: "/geofences",
      icon: Shield,
      isActive: true,
    },
    {
      title: "Settings",
      url: "/settings",
      icon: Settings,
      isActive: true,
      items: [
        {
          title: "Fleet Settings",
          url: "/settings/fleet",
        },
        {
          title: "User Management",
          url: "/settings/users",
        },
        {
          title: "Notifications",
          url: "/settings/notifications",
        },
      ],
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { locale_code } = localeAtom.useValue();

  return (
    <Sidebar
      collapsible="icon"
      {...props}
      variant="floating"
      side={locale_code === "ar" ? "right" : "left"}
      className="glass-sidebar border-0 shadow-2xl"
    >
      {/* Modern Header with Enhanced Branding */}
      <SidebarHeader className="border-sidebar-border/20 border-b p-6">
        <SidebarMenuButton
          size="lg"
          className="btn-modern group data-[state=open]:bg-sidebar-accent/50 data-[state=open]:text-sidebar-accent-foreground hover:bg-sidebar-accent/30 h-auto p-4"
        >
          <div className="gradient-primary flex aspect-square size-14 items-center justify-center rounded-3xl shadow-xl transition-transform duration-300 group-hover:scale-105">
            <div className="relative">
              <svg
                className="size-7 text-white"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
              </svg>
              <div className="bg-success absolute -top-1 -right-1 h-3 w-3 animate-pulse rounded-full"></div>
            </div>
          </div>
          <div className="flex flex-1 flex-col text-left">
            <span className="text-xl font-bold tracking-tight">
              FleetTracker
            </span>
            <span className="text-muted-foreground text-xs font-medium">
              GPS Management Suite
            </span>
            <div className="mt-1 flex items-center gap-1">
              <div className="bg-success h-1.5 w-1.5 animate-pulse rounded-full"></div>
              <span className="text-success text-xs font-medium">
                Live System
              </span>
            </div>
          </div>
        </SidebarMenuButton>

        {/* Quick Stats */}
        <div className="mt-4 grid grid-cols-2 gap-3">
          <div className="glass-card rounded-xl p-3 text-center">
            <div className="text-success text-lg font-bold">12</div>
            <div className="text-muted-foreground text-xs">Online</div>
          </div>
          <div className="glass-card rounded-xl p-3 text-center">
            <div className="text-primary text-lg font-bold">15</div>
            <div className="text-muted-foreground text-xs">Total</div>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-4">
        <NavMain items={data.navMain} />
      </SidebarContent>

      <SidebarFooter className="border-sidebar-border/20 border-t p-4">
        <NavUser />
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}
