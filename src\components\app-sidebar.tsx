import { <PERSON><PERSON>, Car, SquareTerminal } from "lucide-react";
import * as React from "react";

import { localeAtom } from "@/atoms/locale-atom";
import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenuButton,
  SidebarRail,
} from "@/components/ui/sidebar";
import { URLS } from "@/utils/urls";

// This is sample data.
const data = {
  navMain: [
    {
      title: "Units",
      url: URLS.units,
      icon: Car,
      isActive: true,
    },
    {
      title: "Reports",
      url: URLS.reports,
      icon: SquareTerminal,
      isActive: true,
    },
    {
      title: "Models",
      url: "#",
      icon: Bot,
      isActive: true,
      items: [
        {
          title: "Genesis",
          url: "#",
        },
        {
          title: "Explorer",
          url: "#",
        },
        {
          title: "Quantum",
          url: "#",
        },
      ],
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { locale_code } = localeAtom.useValue();

  return (
    <Sidebar
      collapsible="icon"
      {...props}
      variant="floating"
      side={locale_code === "ar" ? "right" : "left"}
      className="glass-sidebar border-r-0"
    >
      <SidebarHeader className="border-sidebar-border/30 border-b pb-4">
        <SidebarMenuButton
          size="lg"
          className="btn-modern data-[state=open]:bg-sidebar-accent/50 data-[state=open]:text-sidebar-accent-foreground hover:bg-sidebar-accent/30"
        >
          <div className="gradient-primary flex aspect-square size-12 items-center justify-center rounded-2xl shadow-lg">
            <svg
              className="size-6 text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
            </svg>
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate text-lg font-bold">GPS Tracking</span>
            <span className="text-muted-foreground truncate text-xs">
              Fleet Management
            </span>
          </div>
        </SidebarMenuButton>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
