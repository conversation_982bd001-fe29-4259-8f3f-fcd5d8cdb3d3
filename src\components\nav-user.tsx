import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  Sparkles,
} from "lucide-react";

import { authAtom } from "@/atoms/auth-atom";
import { openConfirmLogoutDialogAtom } from "@/atoms/open-confirm-logout-dialog-atom";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import i18n from "@/localization/i18n";
import { useTranslation } from "react-i18next";
import ConfirmLogoutDialog from "./auth/confirm-logout-dialog";

export function NavUser() {
  const { isMobile } = useSidebar();
  const { user } = authAtom.useValue();
  const { t } = useTranslation();

  const openConfirmLogoutDialog = () => {
    openConfirmLogoutDialogAtom.open();
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu dir={i18n.language === "ar" ? "rtl" : "ltr"}>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="btn-modern group data-[state=open]:bg-sidebar-accent/50 data-[state=open]:text-sidebar-accent-foreground hover:bg-sidebar-accent/30 h-auto rounded-xl p-4"
            >
              <div className="relative">
                <Avatar className="border-primary/20 h-10 w-10 rounded-xl border-2 shadow-lg">
                  <AvatarImage src={user?.image || ""} alt={user?.name} />
                  <AvatarFallback className="gradient-primary rounded-xl font-semibold text-white">
                    {user?.name?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="bg-success border-background absolute -right-1 -bottom-1 h-4 w-4 rounded-full border-2"></div>
              </div>
              <div className="flex flex-1 flex-col text-left">
                <span className="truncate text-sm font-semibold">
                  {user?.name}
                </span>
                <span className="text-muted-foreground truncate text-xs">
                  {user?.email}
                </span>
                <div className="mt-0.5 flex items-center gap-1">
                  <div className="bg-success h-1.5 w-1.5 animate-pulse rounded-full"></div>
                  <span className="text-success text-xs font-medium">
                    Online
                  </span>
                </div>
              </div>
              <ChevronsUpDown className="ml-auto h-4 w-4 opacity-50 transition-opacity group-hover:opacity-100" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="glass-card w-(--radix-dropdown-menu-trigger-width) min-w-64 rounded-2xl border-0 p-2 shadow-2xl"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={8}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="from-primary/5 to-success/5 flex items-center gap-3 rounded-xl bg-gradient-to-r p-3">
                <Avatar className="border-primary/20 h-12 w-12 rounded-xl border-2 shadow-lg">
                  <AvatarImage src={user?.image || ""} alt={user?.name} />
                  <AvatarFallback className="gradient-primary rounded-xl text-lg font-semibold text-white">
                    {user?.name?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-1 flex-col">
                  <span className="text-base font-semibold">{user?.name}</span>
                  <span className="text-muted-foreground text-xs">
                    {user?.email}
                  </span>
                  <div className="mt-1 flex items-center gap-1">
                    <div className="bg-success h-2 w-2 animate-pulse rounded-full"></div>
                    <span className="text-success text-xs font-medium">
                      Active Session
                    </span>
                  </div>
                </div>
              </div>
            </DropdownMenuLabel>

            <DropdownMenuSeparator className="my-2 opacity-50" />

            <DropdownMenuGroup className="space-y-1">
              <DropdownMenuItem className="btn-modern hover:bg-warning/10 hover:text-warning h-auto cursor-pointer rounded-xl p-3">
                <div className="flex items-center gap-3">
                  <div className="gradient-warning flex h-8 w-8 items-center justify-center rounded-lg">
                    <Sparkles className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex flex-col">
                    <span className="font-medium">Upgrade to Pro</span>
                    <span className="text-muted-foreground text-xs">
                      Unlock premium features
                    </span>
                  </div>
                </div>
              </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator className="my-2 opacity-50" />

            <DropdownMenuGroup className="space-y-1">
              <DropdownMenuItem className="btn-modern hover:bg-primary/10 h-auto cursor-pointer rounded-xl p-3">
                <div className="flex items-center gap-3">
                  <div className="bg-primary/20 flex h-8 w-8 items-center justify-center rounded-lg">
                    <BadgeCheck className="text-primary h-4 w-4" />
                  </div>
                  <span className="font-medium">Account Settings</span>
                </div>
              </DropdownMenuItem>

              <DropdownMenuItem className="btn-modern hover:bg-info/10 h-auto cursor-pointer rounded-xl p-3">
                <div className="flex items-center gap-3">
                  <div className="bg-info/20 flex h-8 w-8 items-center justify-center rounded-lg">
                    <CreditCard className="text-info h-4 w-4" />
                  </div>
                  <span className="font-medium">Billing & Plans</span>
                </div>
              </DropdownMenuItem>

              <DropdownMenuItem className="btn-modern hover:bg-success/10 h-auto cursor-pointer rounded-xl p-3">
                <div className="flex items-center gap-3">
                  <div className="bg-success/20 flex h-8 w-8 items-center justify-center rounded-lg">
                    <Bell className="text-success h-4 w-4" />
                  </div>
                  <div className="flex flex-col">
                    <span className="font-medium">Notifications</span>
                    <span className="text-muted-foreground text-xs">
                      3 unread alerts
                    </span>
                  </div>
                </div>
              </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator className="my-2 opacity-50" />

            <DropdownMenuItem
              onClick={openConfirmLogoutDialog}
              className="btn-modern hover:bg-destructive/10 hover:text-destructive h-auto cursor-pointer rounded-xl p-3"
            >
              <div className="flex items-center gap-3">
                <div className="bg-destructive/20 flex h-8 w-8 items-center justify-center rounded-lg">
                  <LogOut className="text-destructive h-4 w-4" />
                </div>
                <span className="font-medium">{t("auth.logout")}</span>
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>

      <ConfirmLogoutDialog />
    </SidebarMenu>
  );
}
