import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rumb<PERSON><PERSON>,
  <PERSON>readcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { URLS } from "@/utils/urls";
import { Link, useLocation } from "react-router";
import FullScreenToggle from "./utils/full-screen-toggle";
import { LanguageToggle } from "./utils/language-toggle";
import { ModeToggle } from "./utils/mode-toggle";

export default function Navbar() {
  const { pathname } = useLocation();

  const getPageTitle = () => {
    const path = pathname.split("/")[1];
    if (!path) return "Dashboard";
    return path.charAt(0).toUpperCase() + path.slice(1);
  };

  return (
    <header className="glass-nav sticky top-0 z-50 flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-4 px-6">
        <SidebarTrigger className="btn-modern hover:bg-primary/10 hover:text-primary -ml-1" />
        <Separator
          orientation="vertical"
          className="mr-2 h-6 opacity-50 data-[orientation=vertical]:h-6"
        />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink asChild>
                <Link
                  to={URLS.home}
                  className="text-primary hover:text-primary/80 font-semibold transition-colors"
                >
                  GPS Tracking
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            {pathname !== "/" && (
              <BreadcrumbSeparator className="hidden opacity-50 md:block" />
            )}
            <BreadcrumbItem>
              <BreadcrumbPage className="text-foreground font-medium">
                {getPageTitle()}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="ms-auto me-6 flex items-center gap-2">
        <div className="hidden items-center gap-2 sm:flex">
          <LanguageToggle />
          <FullScreenToggle />
        </div>
        <ModeToggle />
      </div>
    </header>
  );
}
