import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  BarChart3,
  Calendar,
  Download,
  FileText,
  MapPin,
  TrendingDown,
  TrendingUp,
  <PERSON>,
  Zap,
} from "lucide-react";

export default function ReportsPage() {
  return (
    <div className="animate-fade-in space-y-8">
      {/* Modern Header */}
      <div className="animate-slide-down flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">
            Analytics & Reports
          </h1>
          <p className="text-muted-foreground mt-2 text-lg">
            Comprehensive insights into your fleet performance
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" className="btn-modern">
            <Calendar className="mr-2 h-4 w-4" />
            Date Range
          </Button>
          <Button className="btn-modern btn-glow">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="glass-card animate-slide-up border-0 shadow-xl">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Distance
            </CardTitle>
            <div className="gradient-primary flex h-10 w-10 items-center justify-center rounded-xl">
              <MapPin className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">24,847 km</div>
            <div className="text-success flex items-center text-xs">
              <TrendingUp className="mr-1 h-3 w-3" />
              +12.5% from last month
            </div>
            <Progress value={75} className="mt-3 h-2" />
          </CardContent>
        </Card>

        <Card
          className="glass-card animate-slide-up border-0 shadow-xl"
          style={{ animationDelay: "0.1s" }}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Vehicles
            </CardTitle>
            <div className="gradient-success flex h-10 w-10 items-center justify-center rounded-xl">
              <Users className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">18</div>
            <div className="text-success flex items-center text-xs">
              <TrendingUp className="mr-1 h-3 w-3" />
              +2 vehicles added
            </div>
            <Progress value={90} className="mt-3 h-2" />
          </CardContent>
        </Card>

        <Card
          className="glass-card animate-slide-up border-0 shadow-xl"
          style={{ animationDelay: "0.2s" }}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Fuel Efficiency
            </CardTitle>
            <div className="gradient-warning flex h-10 w-10 items-center justify-center rounded-xl">
              <Zap className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">7.8 L/100km</div>
            <div className="text-success flex items-center text-xs">
              <TrendingDown className="mr-1 h-3 w-3" />
              -8.2% improvement
            </div>
            <Progress value={65} className="mt-3 h-2" />
          </CardContent>
        </Card>

        <Card
          className="glass-card animate-slide-up border-0 shadow-xl"
          style={{ animationDelay: "0.3s" }}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Reports Generated
            </CardTitle>
            <div className="bg-info flex h-10 w-10 items-center justify-center rounded-xl">
              <FileText className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">127</div>
            <div className="text-success flex items-center text-xs">
              <TrendingUp className="mr-1 h-3 w-3" />
              +23 this month
            </div>
            <Progress value={85} className="mt-3 h-2" />
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-8 lg:grid-cols-3">
        {/* Recent Activity */}
        <Card
          className="glass-card animate-slide-up border-0 shadow-xl lg:col-span-2"
          style={{ animationDelay: "0.4s" }}
        >
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="text-primary h-5 w-5" />
              Fleet Performance Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Vehicle Utilization</p>
                  <p className="text-muted-foreground text-xs">
                    Average daily usage across fleet
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold">87%</p>
                  <p className="text-success text-xs">+5% vs last week</p>
                </div>
              </div>
              <Progress value={87} className="h-3" />

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Route Efficiency</p>
                  <p className="text-muted-foreground text-xs">
                    Optimal route adherence
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold">92%</p>
                  <p className="text-success text-xs">+3% vs last week</p>
                </div>
              </div>
              <Progress value={92} className="h-3" />

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Driver Performance</p>
                  <p className="text-muted-foreground text-xs">
                    Safety and efficiency scores
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold">94%</p>
                  <p className="text-success text-xs">+2% vs last week</p>
                </div>
              </div>
              <Progress value={94} className="h-3" />
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card
          className="glass-card animate-slide-up border-0 shadow-xl"
          style={{ animationDelay: "0.5s" }}
        >
          <CardHeader>
            <CardTitle>Quick Reports</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button
                variant="outline"
                className="btn-modern border-border/50 hover:bg-primary/5 hover:border-primary/20 h-auto w-full justify-start p-4"
              >
                <div className="flex items-center gap-3">
                  <div className="gradient-primary flex h-10 w-10 items-center justify-center rounded-lg">
                    <FileText className="h-5 w-5 text-white" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium">Fleet Summary</p>
                    <p className="text-muted-foreground text-xs">
                      Complete fleet overview
                    </p>
                  </div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="btn-modern border-border/50 hover:bg-success/5 hover:border-success/20 h-auto w-full justify-start p-4"
              >
                <div className="flex items-center gap-3">
                  <div className="gradient-success flex h-10 w-10 items-center justify-center rounded-lg">
                    <Zap className="h-5 w-5 text-white" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium">Fuel Analysis</p>
                    <p className="text-muted-foreground text-xs">
                      Consumption patterns
                    </p>
                  </div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="btn-modern border-border/50 hover:bg-warning/5 hover:border-warning/20 h-auto w-full justify-start p-4"
              >
                <div className="flex items-center gap-3">
                  <div className="gradient-warning flex h-10 w-10 items-center justify-center rounded-lg">
                    <BarChart3 className="h-5 w-5 text-white" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium">Performance Report</p>
                    <p className="text-muted-foreground text-xs">
                      Driver & vehicle metrics
                    </p>
                  </div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="btn-modern border-border/50 hover:bg-info/5 hover:border-info/20 h-auto w-full justify-start p-4"
              >
                <div className="flex items-center gap-3">
                  <div className="bg-info flex h-10 w-10 items-center justify-center rounded-lg">
                    <MapPin className="h-5 w-5 text-white" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium">Route Analysis</p>
                    <p className="text-muted-foreground text-xs">
                      Optimization insights
                    </p>
                  </div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
