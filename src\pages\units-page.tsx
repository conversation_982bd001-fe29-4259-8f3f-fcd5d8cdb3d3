import { DataTable } from "@/components/table/data-table";
import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import useUnitsPage from "@/hooks/use-units-page";
import type { ColumnDef } from "@tanstack/react-table";
import type { LatLngExpression } from "leaflet";
import {
  Edit,
  Filter,
  Layers,
  Locate,
  MapPin,
  MessageCircle,
  Plus,
  Trash2,
  Wifi,
} from "lucide-react";
import { useEffect } from "react";
import { MapContainer, Marker, Popup, TileLayer, useMap } from "react-leaflet";

type Payment = {
  id: string;
  amount: number;
  status: "pending" | "processing" | "success" | "failed";
  email: string;
};

const columns: ColumnDef<Payment>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Status" />;
    },
    cell: ({ row }) => {
      const status = row.original.status;
      return <div>{status}</div>;
    },
  },
  {
    accessorKey: "email",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Email" />;
    },
    cell: ({ row }) => {
      const email = row.original.email;
      return <div>{email}</div>;
    },
  },
  {
    accessorKey: "amount",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Amount" />;
    },
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("amount"));
      const formatted = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(amount);

      return <div>{formatted}</div>;
    },
  },
  {
    id: "actions",
    header: () => <div className="text-start">Actions</div>,
    cell: () => {
      return (
        <div className="flex gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <MessageCircle />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Message</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Locate />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Track</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Wifi />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Data Accuracy</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Edit />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Trash2 color="red" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Delete</p>
            </TooltipContent>
          </Tooltip>
        </div>
      );
    },
  },
];

const data: Payment[] = [
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "processing",
    email: "<EMAIL>",
  },
];

export default function UnitsPage() {
  const { handleResize, isMobile, tableSize } = useUnitsPage();

  // Modern Header Component
  const PageHeader = () => (
    <div className="animate-slide-down mb-8 flex items-center justify-between">
      <div>
        <h1 className="text-4xl font-bold tracking-tight">Fleet Units</h1>
        <p className="text-muted-foreground mt-2 text-lg">
          Monitor and manage your vehicle fleet in real-time
        </p>
      </div>
      <div className="flex items-center gap-3">
        <div className="status-online animate-scale-in rounded-full px-4 py-2 text-sm font-semibold shadow-lg">
          <div className="mr-2 inline-block h-2 w-2 animate-pulse rounded-full bg-current"></div>
          12 Online
        </div>
        <div
          className="status-offline animate-scale-in rounded-full px-4 py-2 text-sm font-semibold shadow-lg"
          style={{ animationDelay: "0.1s" }}
        >
          <div className="mr-2 inline-block h-2 w-2 rounded-full bg-current"></div>
          3 Offline
        </div>
        <div
          className="status-warning animate-scale-in rounded-full px-4 py-2 text-sm font-semibold shadow-lg"
          style={{ animationDelay: "0.2s" }}
        >
          <div className="mr-2 inline-block h-2 w-2 animate-pulse rounded-full bg-current"></div>
          2 Alerts
        </div>
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <div className="animate-fade-in space-y-6">
        <PageHeader />

        <div className="glass-card animate-slide-up rounded-2xl p-6 shadow-2xl">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold">Vehicle List</h2>
            <div className="flex items-center gap-2">
              <Button size="sm" className="btn-modern btn-glow">
                <Plus className="mr-2 h-4 w-4" />
                Add Unit
              </Button>
            </div>
          </div>
          <DataTable columns={columns} data={data} />
        </div>

        <div
          className="glass-card animate-slide-up h-[500px] rounded-2xl p-4 shadow-2xl"
          style={{ animationDelay: "0.2s" }}
        >
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold">Live Map</h2>
            <div className="flex items-center gap-2">
              <Button size="sm" variant="outline" className="btn-modern">
                <MapPin className="mr-2 h-4 w-4" />
                Center Map
              </Button>
            </div>
          </div>
          <div className="h-[calc(100%-4rem)] overflow-hidden rounded-xl">
            <UnitsMap />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="animate-fade-in space-y-6">
      <PageHeader />

      <ResizablePanelGroup
        direction="horizontal"
        className="animate-slide-up border-border/50 overflow-hidden rounded-2xl border shadow-2xl"
      >
        <ResizablePanel
          className="glass-card rounded-l-2xl"
          defaultSize={tableSize}
          onResize={handleResize}
        >
          <div className="flex h-full flex-col p-6">
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-2xl font-semibold">Vehicle Fleet</h2>
              <div className="flex items-center gap-2">
                <Button size="sm" variant="outline" className="btn-modern">
                  <Filter className="mr-2 h-4 w-4" />
                  Filter
                </Button>
                <Button size="sm" className="btn-modern btn-glow">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Unit
                </Button>
              </div>
            </div>
            <div className="flex-1 overflow-hidden">
              <DataTable columns={columns} data={data} />
            </div>
          </div>
        </ResizablePanel>

        <ResizableHandle
          withHandle
          className="bg-border/50 hover:bg-border transition-colors"
        />

        <ResizablePanel className="glass-card rounded-r-2xl">
          <div className="flex h-full flex-col p-6">
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-2xl font-semibold">Live Tracking</h2>
              <div className="flex items-center gap-2">
                <Button size="sm" variant="outline" className="btn-modern">
                  <Layers className="mr-2 h-4 w-4" />
                  Layers
                </Button>
                <Button size="sm" variant="outline" className="btn-modern">
                  <MapPin className="mr-2 h-4 w-4" />
                  Center
                </Button>
              </div>
            </div>
            <div className="flex-1 overflow-hidden rounded-xl">
              <UnitsMap />
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}

const ResizeHandler = () => {
  const map = useMap();
  useEffect(() => {
    setTimeout(() => {
      map.invalidateSize();
    }, 100); // slight delay so container finishes rendering
  }, [map]);
  return null;
};

const UnitsMap = () => {
  const position: LatLngExpression = [26.8206, 30.8025];

  return (
    <MapContainer
      className="z-20 h-full w-full"
      center={position}
      zoom={6}
      scrollWheelZoom={false}
    >
      <ResizeHandler />
      <TileLayer
        className="h-full w-full"
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
      <Marker position={position}>
        <Popup>
          A pretty CSS3 popup. <br /> Easily customizable.
        </Popup>
      </Marker>
    </MapContainer>
  );
};
