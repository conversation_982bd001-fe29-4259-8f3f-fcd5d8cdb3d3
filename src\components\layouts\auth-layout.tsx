import { URLS } from "@/utils/urls";
import { Link, Outlet } from "react-router";
import { FloatingSidebar } from "../auth/floating-sidebar";

export default function AuthLayout() {
  return (
    <div className="gradient-bg relative flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      {/* Modern Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="bg-primary/5 absolute -top-40 -right-40 h-96 w-96 animate-pulse rounded-full blur-3xl"></div>
        <div
          className="bg-success/5 absolute -bottom-40 -left-40 h-96 w-96 animate-pulse rounded-full blur-3xl"
          style={{ animationDelay: "1s" }}
        ></div>
        <div
          className="bg-info/3 absolute top-1/2 left-1/2 h-64 w-64 -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full blur-2xl"
          style={{ animationDelay: "2s" }}
        ></div>
      </div>

      <FloatingSidebar />

      <div className="animate-slide-up relative z-10 w-full max-w-sm md:max-w-4xl">
        <Outlet />
      </div>

      <div
        className="animate-fade-in text-muted-foreground *:[a]:hover:text-primary relative z-10 mt-8 text-center text-xs text-balance *:[a]:font-medium *:[a]:underline *:[a]:underline-offset-4"
        style={{ animationDelay: "0.3s" }}
      >
        By clicking continue, you agree to our{" "}
        <Link to={URLS.termsOfService}>Terms of Service</Link> and{" "}
        <Link to={URLS.privacyPolicy}>Privacy Policy</Link>.
      </div>
    </div>
  );
}
