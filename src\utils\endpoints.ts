import Endpoint from "@mongez/http";

export const endpoint = new Endpoint({
  baseURL: "https://gps.ipsticket.com/api",

  setAuthorizationHeader: () => {
    if (localStorage.getItem("gps-token")) {
      const token = localStorage.getItem("gps-token");
      return `<PERSON><PERSON> ${token}`;
    }
    // return "key some-api-key";
  },

  headers: {
    "Accept-Language": localStorage.getItem("gps-lang") || "en", // default to English
  },
});
