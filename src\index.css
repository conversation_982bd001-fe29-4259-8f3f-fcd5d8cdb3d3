@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Modern GPS Tracking Design System */
@theme inline {
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.25rem;
  --radius-2xl: 1.5rem;

  /* Color System */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /* Status Colors */
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);

  /* Chart Colors */
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  /* Sidebar Colors */
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/* Modern Light Theme - GPS Tracking */
:root {
  --radius: 1rem;

  /* Base Colors - Modern Blue Theme */
  --background: oklch(0.99 0.002 240);
  --foreground: oklch(0.09 0.015 240);

  /* Card System */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.09 0.015 240);

  /* Popover */
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.09 0.015 240);

  /* Primary - Modern Blue Gradient */
  --primary: oklch(0.55 0.22 240);
  --primary-foreground: oklch(0.99 0.002 240);

  /* Secondary - Soft Blue-Gray */
  --secondary: oklch(0.95 0.01 240);
  --secondary-foreground: oklch(0.2 0.03 240);

  /* Muted */
  --muted: oklch(0.97 0.005 240);
  --muted-foreground: oklch(0.45 0.02 240);

  /* Accent */
  --accent: oklch(0.93 0.015 240);
  --accent-foreground: oklch(0.2 0.03 240);

  /* Destructive */
  --destructive: oklch(0.65 0.25 25);
  --destructive-foreground: oklch(0.99 0.002 240);

  /* Status Colors */
  --success: oklch(0.6 0.18 150);
  --success-foreground: oklch(0.99 0.002 240);

  --warning: oklch(0.75 0.2 65);
  --warning-foreground: oklch(0.09 0.015 240);

  --info: oklch(0.6 0.2 220);
  --info-foreground: oklch(0.99 0.002 240);

  /* Borders & Inputs */
  --border: oklch(0.9 0.005 240);
  --input: oklch(0.98 0.002 240);
  --ring: oklch(0.55 0.22 240);

  /* Chart Colors - Modern Palette */
  --chart-1: oklch(0.55 0.22 240);
  --chart-2: oklch(0.6 0.18 150);
  --chart-3: oklch(0.75 0.2 65);
  --chart-4: oklch(0.6 0.2 220);
  --chart-5: oklch(0.7 0.15 300);

  /* Sidebar - Glass Effect */
  --sidebar: oklch(0.98 0.002 240);
  --sidebar-foreground: oklch(0.15 0.02 240);
  --sidebar-primary: oklch(0.55 0.22 240);
  --sidebar-primary-foreground: oklch(0.99 0.002 240);
  --sidebar-accent: oklch(0.93 0.015 240);
  --sidebar-accent-foreground: oklch(0.2 0.03 240);
  --sidebar-border: oklch(0.9 0.005 240);
  --sidebar-ring: oklch(0.55 0.22 240);
}

/* Modern Dark Theme - GPS Tracking */
.dark {
  /* Base Colors */
  --background: oklch(0.05 0.01 240);
  --foreground: oklch(0.95 0.005 240);

  /* Card System */
  --card: oklch(0.08 0.015 240);
  --card-foreground: oklch(0.95 0.005 240);

  /* Popover */
  --popover: oklch(0.08 0.015 240);
  --popover-foreground: oklch(0.95 0.005 240);

  /* Primary - Brighter for Dark Mode */
  --primary: oklch(0.65 0.25 240);
  --primary-foreground: oklch(0.05 0.01 240);

  /* Secondary */
  --secondary: oklch(0.12 0.02 240);
  --secondary-foreground: oklch(0.9 0.005 240);

  /* Muted */
  --muted: oklch(0.1 0.015 240);
  --muted-foreground: oklch(0.65 0.01 240);

  /* Accent */
  --accent: oklch(0.15 0.02 240);
  --accent-foreground: oklch(0.9 0.005 240);

  /* Destructive */
  --destructive: oklch(0.7 0.25 25);
  --destructive-foreground: oklch(0.05 0.01 240);

  /* Status Colors - Dark Mode */
  --success: oklch(0.55 0.18 150);
  --success-foreground: oklch(0.05 0.01 240);

  --warning: oklch(0.7 0.2 65);
  --warning-foreground: oklch(0.05 0.01 240);

  --info: oklch(0.65 0.2 220);
  --info-foreground: oklch(0.05 0.01 240);

  /* Borders & Inputs */
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 8%);
  --ring: oklch(0.65 0.25 240);

  /* Chart Colors - Dark Mode */
  --chart-1: oklch(0.65 0.25 240);
  --chart-2: oklch(0.55 0.18 150);
  --chart-3: oklch(0.7 0.2 65);
  --chart-4: oklch(0.65 0.2 220);
  --chart-5: oklch(0.7 0.15 300);

  /* Sidebar - Dark Glass */
  --sidebar: oklch(0.06 0.012 240);
  --sidebar-foreground: oklch(0.9 0.005 240);
  --sidebar-primary: oklch(0.65 0.25 240);
  --sidebar-primary-foreground: oklch(0.05 0.01 240);
  --sidebar-accent: oklch(0.15 0.02 240);
  --sidebar-accent-foreground: oklch(0.9 0.005 240);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.65 0.25 240);
}

/* .dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
} */

/* Base Styles */
@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Modern Component Styles */
@layer components {
  /* Glass Morphism Effects */
  .glass-card {
    @apply bg-card/80 border-border/50 border shadow-xl backdrop-blur-xl;
  }

  .glass-nav {
    @apply bg-background/80 border-border/50 border-b backdrop-blur-md;
  }

  .glass-sidebar {
    @apply bg-sidebar/90 border-sidebar-border/50 border-r backdrop-blur-lg;
  }

  /* Modern Gradients */
  .gradient-primary {
    background: linear-gradient(
      135deg,
      oklch(0.55 0.22 240) 0%,
      oklch(0.45 0.25 250) 100%
    );
  }

  .gradient-success {
    background: linear-gradient(
      135deg,
      oklch(0.6 0.18 150) 0%,
      oklch(0.5 0.2 160) 100%
    );
  }

  .gradient-warning {
    background: linear-gradient(
      135deg,
      oklch(0.75 0.2 65) 0%,
      oklch(0.65 0.22 55) 100%
    );
  }

  .gradient-bg {
    background: linear-gradient(
      135deg,
      oklch(0.99 0.002 240) 0%,
      oklch(0.97 0.005 250) 50%,
      oklch(0.99 0.002 240) 100%
    );
  }

  .dark .gradient-bg {
    background: linear-gradient(
      135deg,
      oklch(0.05 0.01 240) 0%,
      oklch(0.03 0.015 250) 50%,
      oklch(0.05 0.01 240) 100%
    );
  }

  /* Status Indicators */
  .status-online {
    @apply bg-success text-success-foreground;
  }

  .status-offline {
    @apply bg-muted text-muted-foreground;
  }

  .status-warning {
    @apply bg-warning text-warning-foreground;
  }

  .status-error {
    @apply bg-destructive text-destructive-foreground;
  }

  /* Modern Buttons */
  .btn-modern {
    @apply transform transition-all duration-300 ease-out hover:scale-105 hover:shadow-lg active:scale-95;
  }

  .btn-glow {
    @apply shadow-lg transition-shadow duration-300 hover:shadow-xl;
    box-shadow: 0 0 20px oklch(0.55 0.22 240 / 0.3);
  }

  .dark .btn-glow {
    box-shadow: 0 0 20px oklch(0.65 0.25 240 / 0.4);
  }

  /* Modern Animations */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.4s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.4s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.5s ease-out;
  }
}

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: oklch(0.7 0.05 240);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: oklch(0.6 0.1 240);
}

.dark ::-webkit-scrollbar-thumb {
  background: oklch(0.3 0.05 240);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: oklch(0.4 0.1 240);
}

/* Modern Focus States */
.focus-modern {
  @apply focus:ring-primary/20 focus:border-primary transition-all duration-200 focus:ring-2 focus:outline-none;
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    oklch(0.95 0.005 240) 25%,
    oklch(0.98 0.002 240) 50%,
    oklch(0.95 0.005 240) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.dark .loading-shimmer {
  background: linear-gradient(
    90deg,
    oklch(0.08 0.015 240) 25%,
    oklch(0.12 0.02 240) 50%,
    oklch(0.08 0.015 240) 75%
  );
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
