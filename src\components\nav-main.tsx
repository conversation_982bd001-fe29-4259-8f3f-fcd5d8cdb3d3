"use client";

import { ChevronRight, type LucideIcon } from "lucide-react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { Link, useLocation, useNavigate } from "react-router";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    badge?: string;
    badgeVariant?: "primary" | "success" | "warning" | "destructive";
    items?: {
      title: string;
      url: string;
      icon?: LucideIcon;
    }[];
  }[];
}) {
  const navigate = useNavigate();
  const location = useLocation();

  const getBadgeVariant = (variant?: string) => {
    switch (variant) {
      case "success":
        return "bg-success text-success-foreground";
      case "warning":
        return "bg-warning text-warning-foreground";
      case "destructive":
        return "bg-destructive text-destructive-foreground";
      default:
        return "bg-primary text-primary-foreground";
    }
  };

  const isActiveRoute = (url: string) => {
    return location.pathname === url || location.pathname.startsWith(url + "/");
  };

  return (
    <SidebarGroup className="py-4">
      <SidebarMenu className="space-y-2">
        {items.map((item) => {
          const isActive = isActiveRoute(item.url);

          return (
            <div key={item.title}>
              {item.items ? (
                <Collapsible
                  asChild
                  defaultOpen={item.isActive}
                  className="group/collapsible"
                >
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton
                        tooltip={item.title}
                        className={cn(
                          "btn-modern group h-12 rounded-xl transition-all duration-200",
                          "hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground",
                          "data-[state=open]:bg-sidebar-accent/70",
                          isActive &&
                            "bg-primary/10 text-primary border-primary border-l-4",
                        )}
                      >
                        <div className="flex flex-1 items-center gap-3">
                          {item.icon && (
                            <div
                              className={cn(
                                "flex h-8 w-8 items-center justify-center rounded-lg transition-colors",
                                isActive
                                  ? "bg-primary/20 text-primary"
                                  : "bg-sidebar-accent/30",
                              )}
                            >
                              <item.icon className="h-4 w-4" />
                            </div>
                          )}
                          <span className="font-medium">{item.title}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {item.badge && (
                            <span
                              className={cn(
                                "rounded-full px-2 py-0.5 text-xs font-semibold",
                                getBadgeVariant(item.badgeVariant),
                              )}
                            >
                              {item.badge}
                            </span>
                          )}
                          <ChevronRight className="h-4 w-4 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                        </div>
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="animate-slide-down">
                      <SidebarMenuSub className="mt-2 ml-6 space-y-1">
                        {item.items?.map((subItem) => (
                          <SidebarMenuSubItem key={subItem.title}>
                            <SidebarMenuSubButton
                              asChild
                              className="btn-modern hover:bg-sidebar-accent/30 h-10 rounded-lg"
                            >
                              <Link
                                to={subItem.url}
                                className={cn(
                                  "flex items-center gap-3",
                                  isActiveRoute(subItem.url) &&
                                    "bg-primary/10 text-primary",
                                )}
                              >
                                {subItem.icon && (
                                  <div className="bg-sidebar-accent/20 flex h-6 w-6 items-center justify-center rounded-md">
                                    <subItem.icon className="h-3 w-3" />
                                  </div>
                                )}
                                <span className="text-sm font-medium">
                                  {subItem.title}
                                </span>
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              ) : (
                <SidebarMenuItem>
                  <SidebarMenuButton
                    tooltip={item.title}
                    onClick={() => navigate(item.url)}
                    className={cn(
                      "btn-modern group h-12 rounded-xl transition-all duration-200",
                      "hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground",
                      isActive &&
                        "bg-primary/10 text-primary border-primary border-l-4",
                    )}
                  >
                    <div className="flex flex-1 items-center gap-3">
                      {item.icon && (
                        <div
                          className={cn(
                            "flex h-8 w-8 items-center justify-center rounded-lg transition-colors",
                            isActive
                              ? "bg-primary/20 text-primary"
                              : "bg-sidebar-accent/30",
                          )}
                        >
                          <item.icon className="h-4 w-4" />
                        </div>
                      )}
                      <span className="font-medium">{item.title}</span>
                    </div>
                    {item.badge && (
                      <span
                        className={cn(
                          "rounded-full px-2 py-0.5 text-xs font-semibold",
                          getBadgeVariant(item.badgeVariant),
                        )}
                      >
                        {item.badge}
                      </span>
                    )}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              )}
            </div>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
