import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import useLoginForm from "@/hooks/auth/use-login-form";
import { URLS } from "@/utils/urls";
import { MapPin } from "lucide-react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router";

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const { form, onSubmit, loading } = useLoginForm();
  const { t } = useTranslation();

  return (
    <div className={cn("flex flex-col gap-8", className)} {...props}>
      <Card className="glass-card animate-scale-in overflow-hidden border-0 p-0 shadow-2xl">
        <CardContent className="grid p-0 md:grid-cols-2">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="animate-slide-up border-border/20 space-y-8 border-r p-8 md:p-10"
            >
              <div className="flex flex-col items-center text-center">
                <div className="gradient-primary mb-6 flex h-20 w-20 items-center justify-center rounded-2xl shadow-lg">
                  <MapPin className="h-10 w-10 text-white" />
                </div>
                <h1 className="text-3xl font-bold tracking-tight">
                  {t("auth.welcome_back")}
                </h1>
                <p className="text-muted-foreground mt-2 text-balance">
                  {t("auth.login_to_account")}
                </p>
              </div>

              <div className="space-y-6">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        {t("auth.email")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder={t("auth.enter_email")}
                          className="focus-modern border-border/50 bg-background/50 h-12 rounded-xl px-4 text-base backdrop-blur-sm"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        {t("auth.password")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder={t("auth.enter_password")}
                          className="focus-modern border-border/50 bg-background/50 h-12 rounded-xl px-4 text-base backdrop-blur-sm"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex items-center justify-between">
                <div></div>
                <Link
                  to={URLS.auth.forgotPassword}
                  className="text-primary hover:text-primary/80 text-sm font-medium transition-colors"
                >
                  {t("auth.forgotPassword")}
                </Link>
              </div>

              <Button
                type="submit"
                className="btn-modern btn-glow gradient-primary h-12 w-full rounded-xl text-base font-semibold text-white shadow-lg"
                disabled={loading}
              >
                {loading ? t("auth.logining") : t("auth.login")}
              </Button>

              <div className="relative text-center text-sm">
                <div className="absolute inset-0 flex items-center">
                  <div className="border-border/50 w-full border-t"></div>
                </div>
                <span className="bg-background text-muted-foreground relative px-4">
                  {t("auth.or_continue_with")}
                </span>
              </div>

              <div className="grid grid-cols-3 gap-3">
                <Button
                  variant="outline"
                  type="button"
                  className="btn-modern border-border/50 bg-background/50 hover:bg-accent/50 h-12 rounded-xl backdrop-blur-sm"
                >
                  <svg
                    className="h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701"
                      fill="currentColor"
                    />
                  </svg>
                  <span className="sr-only">{t("auth.login_with_apple")}</span>
                </Button>
                <Button
                  variant="outline"
                  type="button"
                  className="btn-modern border-border/50 bg-background/50 hover:bg-accent/50 h-12 rounded-xl backdrop-blur-sm"
                >
                  <svg
                    className="h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                      fill="currentColor"
                    />
                  </svg>
                  <span className="sr-only">{t("auth.login_with_google")}</span>
                </Button>
                <Button
                  variant="outline"
                  type="button"
                  className="btn-modern border-border/50 bg-background/50 hover:bg-accent/50 h-12 rounded-xl backdrop-blur-sm"
                >
                  <svg
                    className="h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                      fill="currentColor"
                    />
                  </svg>
                  <span className="sr-only">{t("auth.login_with_meta")}</span>
                </Button>
              </div>

              <div className="text-center text-sm">
                <span className="text-muted-foreground">
                  {t("auth.dont_have_account")}
                </span>{" "}
                <Link
                  to={URLS.auth.register}
                  className="text-primary hover:text-primary/80 font-medium transition-colors"
                >
                  {t("auth.sign_up")}
                </Link>
              </div>
            </form>
          </Form>

          {/* Modern Right Side */}
          <div className="gradient-primary relative hidden overflow-hidden md:flex">
            <div className="from-primary/90 to-info/80 absolute inset-0 bg-gradient-to-br"></div>
            <div className="relative flex flex-col items-center justify-center p-12 text-center text-white">
              <div className="animate-bounce-in mb-8">
                <div className="flex h-24 w-24 items-center justify-center rounded-3xl bg-white/20 backdrop-blur-sm">
                  <MapPin className="h-12 w-12" />
                </div>
              </div>
              <h2
                className="animate-slide-up mb-4 text-3xl font-bold"
                style={{ animationDelay: "0.2s" }}
              >
                GPS Fleet Tracking
              </h2>
              <p
                className="animate-slide-up mb-8 text-lg text-white/90"
                style={{ animationDelay: "0.4s" }}
              >
                Monitor your fleet in real-time with precision and reliability
              </p>
              <div
                className="animate-slide-up grid grid-cols-1 gap-4 text-sm"
                style={{ animationDelay: "0.6s" }}
              >
                <div className="flex items-center gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20">
                    <MapPin className="h-4 w-4" />
                  </div>
                  <span>Real-time location tracking</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20">
                    <svg
                      className="h-4 w-4"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                    </svg>
                  </div>
                  <span>Advanced analytics & reports</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20">
                    <svg
                      className="h-4 w-4"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z" />
                    </svg>
                  </div>
                  <span>Secure & reliable platform</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
